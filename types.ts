

export interface GroundingSource {
  uri: string;
  title: string;
}

export interface ProductAnalysis {
  productName: string;
  suggestedTitle: string;
  detailedDescription: string;
  keywords: string[];
  pricing: {
    quickSale: {
      price: number;
      justification: string;
    };
    fairMarket: {
      price: number;
      justification: string;
    };
    ambitious: {
      price: number;
      justification: string;
    };
  };
  sources: GroundingSource[];
}