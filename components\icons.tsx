
import React from 'react';

// Using Heroicons v2 library as a reference for these SVGs

export const TagIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 6h.008v.008H6V6Z" />
  </svg>
);

export const UploadIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" />
  </svg>
);

export const MagnifyingGlassIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
    </svg>
);


export const ClipboardIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a2.25 2.25 0 0 1-2.25 2.25h-1.5a2.25 2.25 0 0 1-2.25-2.25v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184" />
  </svg>
);

export const CheckIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
  </svg>
);

export const ErrorIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z" />
    </svg>
);

export const SparklesIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" {...props}>
        <path fillRule="evenodd" d="M9 4.5a.75.75 0 0 1 .721.544l.82 2.46a.75.75 0 0 0 .544.544l2.46.82a.75.75 0 0 1 0 1.442l-2.46.82a.75.75 0 0 0-.544.544l-.82 2.46a.75.75 0 0 1-1.442 0l-.82-2.46a.75.75 0 0 0-.544-.544l-2.46-.82a.75.75 0 0 1 0-1.442l2.46-.82A.75.75 0 0 0 8.18 5.044l.82-2.46A.75.75 0 0 1 9 4.5ZM18 1.5a.75.75 0 0 1 .728.568l.25 1a.75.75 0 0 0 .568.568l1 .25a.75.75 0 0 1 0 1.456l-1 .25a.75.75 0 0 0-.568.568l-.25 1a.75.75 0 0 1-1.456 0l-.25-1a.75.75 0 0 0-.568-.568l-1-.25a.75.75 0 0 1 0-1.456l1-.25a.75.75 0 0 0 .568.568l.25-1A.75.75 0 0 1 18 1.5ZM18 15a.75.75 0 0 1 .728.568l.25 1a.75.75 0 0 0 .568.568l1 .25a.75.75 0 0 1 0 1.456l-1 .25a.75.75 0 0 0-.568.568l-.25 1a.75.75 0 0 1-1.456 0l-.25-1a.75.75 0 0 0-.568-.568l-1-.25a.75.75 0 0 1 0-1.456l1-.25a.75.75 0 0 0 .568.568l.25-1A.75.75 0 0 1 18 15Z" clipRule="evenodd" />
    </svg>
);

export const TagIconSolid: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
    <path fillRule="evenodd" d="M4.5 3.75a3 3 0 0 0-3 3v10.5a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3V6.75a3 3 0 0 0-3-3h-15Zm4.125 3a.75.75 0 0 0 0 1.5h6.75a.75.75 0 0 0 0-1.5h-6.75Z" clipRule="evenodd" />
  </svg>
);

export const DocumentTextIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
    <path fillRule="evenodd" d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a.375.375 0 0 1-.375-.375V6.75A3.75 3.75 0 0 0 9 3H5.625Zm3.75 14.25a.75.75 0 0 1 0-1.5h4.5a.75.75 0 0 1 0 1.5h-4.5Zm0-3a.75.75 0 0 1 0-1.5h4.5a.75.75 0 0 1 0 1.5h-4.5Z" clipRule="evenodd" />
  </svg>
);

export const PriceTagIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
    <path d="M11.47 3.841a.75.75 0 0 1 1.06 0l8.69 8.69a.75.75 0 1 1-1.06 1.061l-8.689-8.69a.75.75 0 0 1 0-1.06Z" />
    <path d="m12.25 5.25.008-.007.007.007-.007.008-.008-.008Zm.742.742.007-.008.008.007-.008.008-.007-.007ZM12.25 6.75l.008-.007.007.007-.007.008-.008-.008Zm.742.742.007-.008.008.007-.008.008-.007-.007ZM13.75 5.25l.008-.007.007.007-.007.008-.008-.008Zm.742.742.007-.008.008.007-.008.008-.007-.007ZM13.75 6.75l.008-.007.007.007-.007.008-.008-.008Zm.742.742.007-.008.008.007-.008.008-.007-.007ZM15.25 5.25l.008-.007.007.007-.007.008-.008-.008Zm.742.742.007-.008.008.007-.008.008-.007-.007ZM15.25 6.75l.008-.007.007.007-.007.008-.008-.008Zm.742.742.007-.008.008.007-.008.008-.007-.007ZM9.25 8.25l.008-.007.007.007-.007.008-.008-.008Zm.742.742.007-.008.008.007-.008.008-.007-.007ZM9.25 9.75l.008-.007.007.007-.007.008-.008-.008Zm.742.742.007-.008.008.007-.008.008-.007-.007ZM10.75 8.25l.008-.007.007.007-.007.008-.008-.008Zm.742.742.007-.008.008.007-.008.008-.007-.007ZM10.75 9.75l.008-.007.007.007-.007.008-.008-.008Zm.742.742.007-.008.008.007-.008.008-.007-.007Z" />
    <path d="M4.94 12.06a.75.75 0 0 1 0-1.061l4.01-4.01a.75.75 0 0 1 1.06 0l4.01 4.01a.75.75 0 0 1-1.06 1.06L12 10.06l-1.47 1.47a.75.75 0 0 1-1.06 0L8.001 10.06l-3.06 3.061a.75.75 0 0 1-1.06 0Z" />
    <path d="M3 12.75a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75Z" />
    <path d="M4.125 15a.75.75 0 0 1 .75-.75h14.25a.75.75 0 0 1 0 1.5H4.875a.75.75 0 0 1-.75-.75Z" />
    <path d="M4.875 17.25a.75.75 0 0 1 .75-.75h12.75a.75.75 0 0 1 0 1.5H5.625a.75.75 0 0 1-.75-.75Z" />
    <path d="M5.625 19.5a.75.75 0 0 1 .75-.75h11.25a.75.75 0 0 1 0 1.5H6.375a.75.75 0 0 1-.75-.75Z" />
  </svg>
);

export const RefreshIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 11.667 0l3.181-3.183m-11.667 0-3.181 3.183m0 0-3.181-3.183m3.181 3.183L9.348 16.023m-4.992-4.993 3.181-3.183a8.25 8.25 0 0 1 11.667 0l3.181 3.183" />
  </svg>
);

export const XCircleIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
        <path fillRule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z" clipRule="evenodd" />
    </svg>
);

export const GlobeAltIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 21a9 9 0 1 0 0-18 9 9 0 0 0 0 18Z" />
      <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 12h16.5m-16.5 0a9 9 0 0 1 16.5 0M12 3c-3.75 0-7.042 2.06-8.52 5M12 21c3.75 0 7.042-2.06 8.52-5" />
    </svg>
);
