
import React from 'react';
import type { ProductAnalysis } from '../types.ts';
import { CopyButton } from './CopyButton.tsx';
import { SparklesIcon, TagIconSolid, DocumentTextIcon, PriceTagIcon, RefreshIcon, GlobeAltIcon } from './icons.tsx';

interface ResultsDisplayProps {
  result: ProductAnalysis;
  onReset: () => void;
}

const PriceCard: React.FC<{ title: string; price: number; justification: string; tier: 'quick' | 'fair' | 'ambitious' }> = ({ title, price, justification, tier }) => {
  const tierColors = {
    quick: 'border-green-500 bg-green-50 dark:bg-green-900/20',
    fair: 'border-blue-500 bg-blue-50 dark:bg-blue-900/20',
    ambitious: 'border-purple-500 bg-purple-50 dark:bg-purple-900/20',
  };

  return (
    <div className={`p-4 rounded-lg border ${tierColors[tier]} flex flex-col`}>
      <h4 className="font-semibold text-slate-700 dark:text-slate-200">{title}</h4>
      <p className="text-2xl font-bold text-slate-900 dark:text-white my-1">${price.toFixed(2)}</p>
      <p className="text-sm text-slate-500 dark:text-slate-400 flex-grow">{justification}</p>
    </div>
  );
};

export const ResultsDisplay: React.FC<ResultsDisplayProps> = ({ result, onReset }) => {
  return (
    <div className="bg-white dark:bg-slate-800 p-6 sm:p-8 rounded-2xl shadow-lg animate-fade-in space-y-8">
      
      <div className="text-center">
        <h2 className="text-2xl sm:text-3xl font-bold text-primary-600 dark:text-primary-400">Analysis Complete!</h2>
        <p className="text-slate-600 dark:text-slate-300 mt-1">Here's a breakdown for your <strong className="text-slate-800 dark:text-slate-100">{result.productName}</strong>.</p>
      </div>

      <div className="space-y-6">
        {/* Suggested Title */}
        <div className="p-4 bg-slate-100 dark:bg-slate-700/50 rounded-lg">
          <h3 className="flex items-center gap-2 font-semibold text-lg text-slate-800 dark:text-slate-100 mb-2">
            <SparklesIcon className="w-5 h-5 text-primary-500"/>
            Suggested Title
          </h3>
          <div className="flex items-center gap-2">
            <p className="flex-grow text-slate-700 dark:text-slate-300 italic">"{result.suggestedTitle}"</p>
            <CopyButton textToCopy={result.suggestedTitle} />
          </div>
        </div>

        {/* Detailed Description */}
        <div>
          <h3 className="flex items-center gap-2 font-semibold text-lg text-slate-800 dark:text-slate-100 mb-2">
             <DocumentTextIcon className="w-5 h-5 text-primary-500"/>
            Detailed Description
          </h3>
          <div className="relative p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
             <div className="absolute top-2 right-2">
                <CopyButton textToCopy={result.detailedDescription} />
             </div>
             <p className="text-slate-600 dark:text-slate-300 whitespace-pre-wrap">{result.detailedDescription}</p>
          </div>
        </div>

        {/* Keywords */}
        <div>
          <h3 className="flex items-center gap-2 font-semibold text-lg text-slate-800 dark:text-slate-100 mb-2">
            <TagIconSolid className="w-5 h-5 text-primary-500"/>
            Keywords
          </h3>
          <div className="relative p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
             <div className="absolute top-2 right-2">
                <CopyButton textToCopy={result.keywords.join(', ')} />
             </div>
             <div className="flex flex-wrap gap-2">
                {result.keywords.map((keyword, index) => (
                    <span key={index} className="bg-primary-100 dark:bg-primary-900/50 text-primary-800 dark:text-primary-200 text-sm font-medium px-2.5 py-0.5 rounded-full">
                        {keyword}
                    </span>
                ))}
             </div>
          </div>
        </div>

        {/* Pricing */}
        <div>
          <h3 className="flex items-center gap-2 font-semibold text-lg text-slate-800 dark:text-slate-100 mb-3">
             <PriceTagIcon className="w-5 h-5 text-primary-500"/>
            Pricing Tiers
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
             <PriceCard title="Quick Sale" price={result.pricing.quickSale.price} justification={result.pricing.quickSale.justification} tier="quick" />
             <PriceCard title="Fair Market" price={result.pricing.fairMarket.price} justification={result.pricing.fairMarket.justification} tier="fair" />
             <PriceCard title="Ambitious Price" price={result.pricing.ambitious.price} justification={result.pricing.ambitious.justification} tier="ambitious" />
          </div>
        </div>

        {/* Sources */}
        {result.sources && result.sources.length > 0 && (
          <div>
              <h3 className="flex items-center gap-2 font-semibold text-lg text-slate-800 dark:text-slate-100 mb-3">
                  <GlobeAltIcon className="w-5 h-5 text-primary-500"/>
                  Comparable Listings Found Online
              </h3>
              <div className="p-2 border border-slate-200 dark:border-slate-700 rounded-lg space-y-2">
                  {result.sources.map((source, index) => (
                      <a
                          key={index}
                          href={source.uri}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="block p-3 rounded-md bg-slate-50 dark:bg-slate-700/50 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors group"
                      >
                          <p className="font-medium text-primary-700 dark:text-primary-300 group-hover:underline truncate">{source.title}</p>
                          <p className="text-sm text-slate-500 dark:text-slate-400 truncate">{source.uri}</p>
                      </a>
                  ))}
              </div>
              <p className="text-xs text-slate-400 dark:text-slate-500 mt-2 text-center">
                  Note: These sources were used by the AI to help determine pricing.
              </p>
          </div>
        )}
      </div>

      <div className="pt-6 border-t border-slate-200 dark:border-slate-700">
         <button
            onClick={onReset}
            className="w-full flex items-center justify-center gap-2 bg-slate-200 dark:bg-slate-700 text-slate-800 dark:text-slate-100 font-bold py-3 px-6 rounded-lg hover:bg-slate-300 dark:hover:bg-slate-600 focus:outline-none focus:ring-4 focus:ring-slate-500/30 transition-all"
          >
            <RefreshIcon className="w-5 h-5" />
            Analyze Another Item
          </button>
      </div>
    </div>
  );
};