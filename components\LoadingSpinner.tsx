
import React from 'react';

export const LoadingSpinner: React.FC = () => {
    const messages = [
        "Consulting with e-commerce experts...",
        "Analyzing product details...",
        "Calculating market value...",
        "Polishing your product description...",
        "Finding the perfect keywords...",
    ];
    
    const [message, setMessage] = React.useState(messages[0]);

    React.useEffect(() => {
        let index = 0;
        const interval = setInterval(() => {
            index = (index + 1) % messages.length;
            setMessage(messages[index]);
        }, 3000);

        return () => clearInterval(interval);
    }, []);


  return (
    <div className="flex flex-col items-center justify-center p-8 bg-white dark:bg-slate-800 rounded-2xl shadow-lg">
      <svg
        className="animate-spin -ml-1 mr-3 h-10 w-10 text-primary-600"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        ></circle>
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      <h2 className="mt-4 text-lg font-semibold text-slate-700 dark:text-slate-200">
        AI is at work...
      </h2>
      <p className="mt-2 text-sm text-slate-500 dark:text-slate-400 text-center transition-opacity duration-500">{message}</p>
    </div>
  );
};
