
import React, { useState, useCallback } from 'react';
import { Header } from './components/Header.tsx';
import { ProductForm } from './components/ProductForm.tsx';
import { ResultsDisplay } from './components/ResultsDisplay.tsx';
import { LoadingSpinner } from './components/LoadingSpinner.tsx';
import { identifyAndPriceProduct } from './services/geminiService.ts';
import type { ProductAnalysis } from './types.ts';
import { ErrorIcon } from './components/icons.tsx';

const App: React.FC = () => {
  const [analysisResult, setAnalysisResult] = useState<ProductAnalysis | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleAnalysis = useCallback(async (images: string[], description: string) => {
    setIsLoading(true);
    setError(null);
    setAnalysisResult(null);

    try {
      if (!process.env.API_KEY) {
        throw new Error("API key is not configured. Please set the API_KEY environment variable.");
      }
      const result = await identifyAndPriceProduct(images, description);
      setAnalysisResult(result);
    } catch (err) {
      console.error(err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleReset = useCallback(() => {
    setAnalysisResult(null);
    setError(null);
    setIsLoading(false);
  }, []);

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 text-slate-900 dark:text-slate-100 font-sans">
      <Header />
      <main className="container mx-auto px-4 py-8 md:py-12">
        <div className="max-w-4xl mx-auto">
          {!analysisResult && !isLoading && (
            <ProductForm onAnalyze={handleAnalysis} />
          )}

          {isLoading && <LoadingSpinner />}
          
          {error && (
            <div className="bg-red-100 dark:bg-red-900/20 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg relative text-center" role="alert">
              <div className="flex items-center justify-center">
                <ErrorIcon className="w-6 h-6 mr-2" />
                <strong className="font-bold">Error:</strong>
                <span className="block sm:inline ml-2">{error}</span>
              </div>
            </div>
          )}

          {analysisResult && !isLoading && (
            <ResultsDisplay result={analysisResult} onReset={handleReset} />
          )}
        </div>
      </main>
      <footer className="text-center py-4 text-slate-500 dark:text-slate-400 text-sm">
        <p>Powered by AI. Always verify pricing and details before listing.</p>
      </footer>
    </div>
  );
};

export default App;