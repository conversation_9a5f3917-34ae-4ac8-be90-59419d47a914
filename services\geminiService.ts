
import { GoogleGenAI } from "@google/genai";
import type { ProductAnalysis, GroundingSource } from '../types.ts';

function base64ToInlineData(base64String: string) {
    const match = base64String.match(/^data:(image\/(?:jpeg|png|webp));base64,(.*)$/);
    if (!match) {
        // Fallback for strings that might be missing the prefix
        console.warn("Base64 string is missing the data URI prefix. Assuming image/jpeg.");
        return {
            inlineData: {
                mimeType: 'image/jpeg',
                data: base64String,
            },
        };
    }
    return {
        inlineData: {
            mimeType: match[1],
            data: match[2],
        },
    };
}

const jsonStructure = `{
  "productName": "A concise name for the identified product.",
  "suggestedTitle": "A compelling, SEO-friendly title for an online marketplace listing.",
  "detailedDescription": "A detailed product description suitable for a marketplace listing.",
  "keywords": ["A list of 5-10 relevant keywords."],
  "pricing": {
    "quickSale": {"price": 100, "justification": "Justification based on search results."},
    "fairMarket": {"price": 150, "justification": "Justification based on search results."},
    "ambitious": {"price": 200, "justification": "Justification based on search results."}
  }
}`;

const systemInstruction = `You are an expert e-commerce assistant specializing in the used goods market. Your task is to analyze one or more images of a product and a user-provided description to help the user create a compelling online listing.
1.  **Identify the Product:** From the provided images and text, identify the product, including brand and model if possible.
2.  **Create a Title:** Generate a catchy, descriptive title optimized for search engines.
3.  **Write a Description:** Draft a detailed, professional description. Mention that it's a used item and describe its potential features and benefits. Do not make up condition details not visible in the images.
4.  **Suggest Keywords:** Provide a list of relevant keywords for tagging the listing.
5.  **Propose Pricing Based on Research:** YOU MUST USE Google Search to find comparable current and sold listings for this item in a used condition. Suggest three pricing tiers in USD: 'Quick Sale', 'Fair Market', and 'Ambitious'. The justification for each price MUST be based on the search results, citing trends or specific examples you found. For example, 'Based on several sold listings on eBay for around $50, a quick sale price is...'
Your response MUST be a single, valid JSON object that strictly adheres to the structure provided below. Do not include any text, markdown, or formatting like \\\`\\\`\\\`json before or after the JSON object.
${jsonStructure}`;

export async function identifyAndPriceProduct(
  imageBases64: string[],
  userDescription: string
): Promise<ProductAnalysis> {
  const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

  const imageParts = imageBases64.map(base64ToInlineData);
  const textPart = {
    text: `User's notes about the item: "${userDescription}"`,
  };

  const response = await ai.models.generateContent({
    model: "gemini-2.5-flash",
    contents: { parts: [textPart, ...imageParts] },
    config: {
      systemInstruction,
      temperature: 0.3,
      tools: [{googleSearch: {}}],
    },
  });

  const groundingChunks = response.candidates?.[0]?.groundingMetadata?.groundingChunks ?? [];
  const sources: GroundingSource[] = groundingChunks
      .map(chunk => chunk.web)
      .filter((web): web is { uri: string; title: string } => !!(web?.uri && web.title))
      .map(web => ({ uri: web.uri, title: web.title }));
  
  const uniqueSources = Array.from(new Map(sources.map(s => [s.uri, s])).values());

  const responseText = response.text;
  const jsonStart = responseText.indexOf('{');
  const jsonEnd = responseText.lastIndexOf('}');

  if (jsonStart === -1 || jsonEnd === -1 || jsonEnd < jsonStart) {
    console.error("Failed to find JSON object in response:", responseText);
    throw new Error("AI returned an invalid response. Please try again.");
  }

  const jsonText = responseText.substring(jsonStart, jsonEnd + 1);
  
  try {
    const analysisPart: Omit<ProductAnalysis, 'sources'> = JSON.parse(jsonText);
    const result: ProductAnalysis = {
        ...analysisPart,
        sources: uniqueSources,
    };
    return result;
  } catch(e) {
    console.error("Failed to parse JSON response:", jsonText);
    // Always return a fallback result so the UI can display an error
    return {
      productName: "Unknown Product",
      suggestedTitle: "No title available",
      detailedDescription: "No description available. Gemini did not return valid results.",
      keywords: [],
      pricing: {
        quickSale: { price: 0, justification: "No pricing available." },
        fairMarket: { price: 0, justification: "No pricing available." },
        ambitious: { price: 0, justification: "No pricing available." }
      },
      sources: []
    };
  }
}